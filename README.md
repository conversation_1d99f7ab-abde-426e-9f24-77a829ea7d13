# ApexStream

Sistema avançado de processamento de vídeo para streaming HLS com suporte a **múltiplas qualidades simultâneas** (480p, 720p, 1080p) e **progresso em tempo real via WebSocket**.

## ✨ Principais Funcionalidades

- 🎥 **Processamento Multi-Qualidade**: Converte vídeos automaticamente em 480p, 720p e 1080p
- ⚡ **Processamento Assíncrono**: Processa todas as qualidades em paralelo usando goroutines
- 📡 **Progresso em Tempo Real**: WebSocket com progresso individual de cada qualidade
- 🎬 **HLS Streaming**: Gera playlists HLS compatíveis com todos os players
- 📊 **Monitoramento Avançado**: Acompanhe o progresso de cada qualidade separadamente
- 🔄 **Sistema de Filas**: Redis para gerenciamento robusto de jobs
- ☁️ **Storage Flexível**: Suporte a Cloudflare R2 e armazenamento local
- 🔐 **API Segura**: Autenticação via API Key
- 📈 **Métricas Detalhadas**: Estatísticas completas de processamento

## 🚀 Início Rápido

### Pré-requisitos

- Go 1.21+
- FFmpeg
- Redis
- Docker (opcional)

### Instalação

```bash
# Clone o repositório
git clone https://github.com/seu-usuario/apexstream.git
cd apexstream

# Instale as dependências
go mod tidy

# Configure as variáveis de ambiente
cp .env.example .env
# Edite o arquivo .env com suas configurações

# Execute o servidor
go run cmd/server/main.go
```

### Configuração Básica (.env)

```bash
# Servidor
PORT=8080
ENVIRONMENT=development

# Processamento
VIDEO_DIR=./videos
MAX_FILE_SIZE=104857600  # 100MB
ENABLE_FFMPEG=true

# Redis (para filas)
ENABLE_QUEUE=true
REDIS_HOST=localhost
REDIS_PORT=6379
WORKER_POOL_SIZE=4

# Autenticação
ENABLE_AUTH=true
API_KEYS=sua-api-key-aqui

# Storage (opcional)
ENABLE_STORAGE=true
STORAGE_TYPE=local  # ou r2
```

## 📖 Uso

### 1. Upload de Vídeo

```bash
curl -X POST http://localhost:8080/api/v1/videos/upload \
  -H "Authorization: Bearer sua-api-key" \
  -F "video=@seu-video.mp4"
```

**Resposta:**
```json
{
  "status": "success",
  "message": "Vídeo enviado para processamento",
  "job_id": "job_abc123def456",
  "websocket_url": "ws://localhost:8080/api/v1/ws/job_abc123def456"
}
```

### 2. Monitoramento via WebSocket

```javascript
const ws = new WebSocket('ws://localhost:8080/api/v1/ws/job_abc123def456');

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  if (data.type === 'progress') {
    console.log(`Progresso geral: ${data.progress}%`);
    console.log(`Qualidade atual: ${data.current_quality}`);
    
    // Progresso individual de cada qualidade
    data.quality_progress.forEach(quality => {
      console.log(`${quality.quality}: ${quality.progress}% - ${quality.status}`);
    });
  }
};
```

### 3. Estrutura de Arquivos Gerados

```
/videos/processed/job_abc123def456/
├── master.m3u8          # Playlist principal (ABR)
├── 480p/
│   ├── playlist.m3u8    # Playlist 480p
│   └── segment_*.ts     # Segmentos 480p
├── 720p/
│   ├── playlist.m3u8    # Playlist 720p
│   └── segment_*.ts     # Segmentos 720p
└── 1080p/
    ├── playlist.m3u8    # Playlist 1080p
    └── segment_*.ts     # Segmentos 1080p
```

### 4. Reprodução no Player

```html
<video controls>
  <source src="http://localhost:8080/api/v1/videos/job_abc123def456/master.m3u8" type="application/x-mpegURL">
</video>
```

## 🔧 Configuração Avançada

### Qualidades Personalizadas

```json
{
  "process_in_parallel": true,
  "qualities": [
    {
      "name": "480p",
      "width": 854,
      "height": 480,
      "bitrate": 1000,
      "audio_rate": 128,
      "max_rate": 1200,
      "buf_size": 2000
    },
    {
      "name": "720p",
      "width": 1280,
      "height": 720,
      "bitrate": 2500,
      "audio_rate": 128,
      "max_rate": 3000,
      "buf_size": 5000
    },
    {
      "name": "1080p",
      "width": 1920,
      "height": 1080,
      "bitrate": 5000,
      "audio_rate": 192,
      "max_rate": 6000,
      "buf_size": 10000
    }
  ]
}
```

### Docker Compose

```yaml
version: '3.8'
services:
  apexstream:
    build: .
    ports:
      - "8080:8080"
    environment:
      - REDIS_HOST=redis
      - ENABLE_QUEUE=true
    depends_on:
      - redis
    volumes:
      - ./videos:/app/videos

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
```

## 📊 Monitoramento

### Progresso em Tempo Real

O sistema envia atualizações detalhadas via WebSocket:

```json
{
  "type": "progress",
  "job_id": "job_abc123def456",
  "progress": 65.5,
  "status": "processing",
  "current_quality": "720p",
  "quality_progress": [
    {
      "quality": "480p",
      "progress": 100,
      "status": "completed",
      "message": "Processamento concluído",
      "started_at": "2024-01-15T10:30:05Z",
      "completed_at": "2024-01-15T10:32:15Z"
    },
    {
      "quality": "720p",
      "progress": 75,
      "status": "processing",
      "message": "Processando segmento 30/40",
      "started_at": "2024-01-15T10:30:05Z"
    },
    {
      "quality": "1080p",
      "progress": 0,
      "status": "pending",
      "message": "Aguardando processamento"
    }
  ]
}
```

### Métricas de Sistema

```bash
# Status dos workers
curl -H "Authorization: Bearer sua-api-key" \
  http://localhost:8080/api/v1/ws/stats

# Saúde do sistema
curl http://localhost:8080/api/v1/ws/health
```

## 🏗️ Arquitetura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Upload API    │───▶│   Queue Redis   │───▶│  Worker Pool    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             ▼
                       │  WebSocket Hub  │    ┌─────────────────┐
                       └─────────────────┘    │   FFmpeg Proc   │
                                │             └─────────────────┘
                                ▼                       │
                       ┌─────────────────┐             ▼
                       │   Frontend      │    ┌─────────────────┐
                       └─────────────────┘    │   HLS Output    │
                                              └─────────────────┘
```

## 📚 Documentação

- [📖 Documentação da API](docs/API_DOCUMENTATION.md)
- [🔧 Guia de Integração](docs/INTEGRATION_GUIDE.md)
- [📡 WebSocket Progress](docs/WEBSOCKET_PROGRESS.md)
- [☁️ Integração Cloudflare R2](docs/R2_INTEGRATION.md)
---

**ApexStream** - Processamento de vídeo de alta performance para a web moderna 🚀
