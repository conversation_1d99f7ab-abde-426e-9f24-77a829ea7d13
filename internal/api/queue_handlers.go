package api

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"apexstream/internal/queue"

	"github.com/gin-gonic/gin"
)

// QueueConfig contém configurações para handlers com fila
type QueueConfig struct {
	VideoDir      string
	MaxFileSize   int64
	ProcessedDir  string
	QueueManager  *queue.QueueManager
	EnableStorage bool
	StorageType   string
}

// QueueUploadResponse resposta do upload com fila
type QueueUploadResponse struct {
	Success   bool   `json:"success"`
	JobID     string `json:"job_id"`
	Message   string `json:"message"`
	Filename  string `json:"filename"`
	Size      int64  `json:"size"`
	Timestamp int64  `json:"timestamp"`
	Status    string `json:"status"`
}

// JobStatusResponse resposta do status do job
type JobStatusResponse struct {
	Success bool            `json:"success"`
	Job     *queue.VideoJob `json:"job,omitempty"`
	Error   string          `json:"error,omitempty"`
	Message string          `json:"message,omitempty"`
}

// QueueStatsResponse resposta das estatísticas da fila
type QueueStatsResponse struct {
	Success bool                   `json:"success"`
	Stats   map[string]interface{} `json:"stats,omitempty"`
	Error   string                 `json:"error,omitempty"`
}

// QueueUploadHandler handler de upload que usa filas
func QueueUploadHandler(config *QueueConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Verificar se o queue manager está disponível
		if config.QueueManager == nil {
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Success: false,
				Error:   "queue_unavailable",
				Message: "Sistema de filas não está disponível",
			})
			return
		}

		// Obter arquivo do form
		file, header, err := c.Request.FormFile("video")
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Success: false,
				Error:   "missing_file",
				Message: "Arquivo de vídeo é obrigatório",
			})
			return
		}
		defer file.Close()

		// Validar tamanho do arquivo
		if header.Size > config.MaxFileSize {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Success: false,
				Error:   "file_too_large",
				Message: fmt.Sprintf("Arquivo muito grande. Máximo permitido: %d bytes", config.MaxFileSize),
			})
			return
		}

		// Validar tipo de arquivo
		if !isValidVideoFile(header.Filename) {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Success: false,
				Error:   "invalid_file_type",
				Message: "Tipo de arquivo não suportado. Use: .mp4, .avi, .mov, .mkv",
			})
			return
		}

		// Gerar ID único para o upload
		uploadID := generateUploadID()

		// Criar diretório de entrada se não existir
		if err := os.MkdirAll(config.VideoDir, 0755); err != nil {
			log.Printf("Erro ao criar diretório: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Success: false,
				Error:   "directory_error",
				Message: "Erro interno do servidor",
			})
			return
		}

		// Salvar arquivo temporariamente
		inputPath := filepath.Join(config.VideoDir, fmt.Sprintf("%s_%s", uploadID, header.Filename))
		outputFile, err := os.Create(inputPath)
		if err != nil {
			log.Printf("Erro ao criar arquivo: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Success: false,
				Error:   "file_creation_error",
				Message: "Erro ao salvar arquivo",
			})
			return
		}
		defer outputFile.Close()

		// Copiar arquivo
		bytesWritten, err := io.Copy(outputFile, file)
		if err != nil {
			log.Printf("Erro ao copiar arquivo: %v", err)
			os.Remove(inputPath) // Limpar arquivo parcial
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Success: false,
				Error:   "file_copy_error",
				Message: "Erro ao processar arquivo",
			})
			return
		}

		// Criar configuração de processamento
		processingConfig := &queue.VideoProcessingConfig{
			SegmentTime:   10,
			PlaylistType:  "vod",
			VideoCodec:    "libx264",
			AudioCodec:    "aac",
			VideoBitrates: []int{1000, 2000, 4000},
			EnableStorage: config.EnableStorage,
			StorageType:   config.StorageType,
		}

		// Criar job
		outputDir := filepath.Join(config.ProcessedDir, uploadID)
		job := queue.NewVideoJob(inputPath, outputDir, processingConfig)

		// Definir metadados adicionais
		job.Metadata.OriginalName = header.Filename
		job.Metadata.FileSize = bytesWritten
		job.Metadata.ContentType = header.Header.Get("Content-Type")
		job.Metadata.ClientIP = c.ClientIP()
		job.Metadata.UserAgent = c.GetHeader("User-Agent")

		// Adicionar job à fila
		if err := config.QueueManager.Enqueue(c.Request.Context(), job); err != nil {
			log.Printf("Erro ao enfileirar job: %v", err)
			os.Remove(inputPath) // Limpar arquivo
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Success: false,
				Error:   "queue_error",
				Message: "Erro ao enfileirar processamento",
			})
			return
		}

		log.Printf("Upload concluído e job enfileirado: %s (%d bytes)", job.ID, bytesWritten)

		// Resposta de sucesso
		c.JSON(http.StatusOK, QueueUploadResponse{
			Success:   true,
			JobID:     job.ID,
			Message:   "Upload concluído com sucesso. Processamento iniciado.",
			Filename:  header.Filename,
			Size:      bytesWritten,
			Timestamp: time.Now().Unix(),
			Status:    string(queue.JobStatusPending),
		})
	}
}

// JobStatusHandler retorna o status de um job
func JobStatusHandler(config *QueueConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		jobID := c.Param("id")
		if jobID == "" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Success: false,
				Error:   "missing_job_id",
				Message: "ID do job é obrigatório",
			})
			return
		}

		// Buscar job
		job, err := config.QueueManager.GetJobStatus(c.Request.Context(), jobID)
		if err != nil {
			if strings.Contains(err.Error(), "não encontrado") {
				c.JSON(http.StatusNotFound, JobStatusResponse{
					Success: false,
					Error:   "job_not_found",
					Message: "Job não encontrado",
				})
				return
			}

			log.Printf("Erro ao buscar job %s: %v", jobID, err)
			c.JSON(http.StatusInternalServerError, JobStatusResponse{
				Success: false,
				Error:   "internal_error",
				Message: "Erro interno do servidor",
			})
			return
		}

		c.JSON(http.StatusOK, JobStatusResponse{
			Success: true,
			Job:     job,
		})
	}
}

// QueueStatsHandler retorna estatísticas das filas
func QueueStatsHandler(config *QueueConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		stats, err := config.QueueManager.GetQueueStats(c.Request.Context())
		if err != nil {
			log.Printf("Erro ao obter estatísticas: %v", err)
			c.JSON(http.StatusInternalServerError, QueueStatsResponse{
				Success: false,
				Error:   "stats_error",
			})
			return
		}

		c.JSON(http.StatusOK, QueueStatsResponse{
			Success: true,
			Stats:   stats,
		})
	}
}

// ListJobsHandler lista jobs de uma fila
func ListJobsHandler(config *QueueConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		queueName := c.DefaultQuery("queue", "normal")
		limitStr := c.DefaultQuery("limit", "10")
		offsetStr := c.DefaultQuery("offset", "0")

		limit, err := strconv.ParseInt(limitStr, 10, 64)
		if err != nil || limit <= 0 {
			limit = 10
		}

		offset, err := strconv.ParseInt(offsetStr, 10, 64)
		if err != nil || offset < 0 {
			offset = 0
		}

		// Mapear nome da fila
		var queueKey string
		switch queueName {
		case "high":
			queueKey = queue.QueueNames.High
		case "low":
			queueKey = queue.QueueNames.Low
		case "processing":
			queueKey = queue.QueueNames.Processing
		case "completed":
			queueKey = queue.QueueNames.Completed
		case "failed":
			queueKey = queue.QueueNames.Failed
		case "dead_letter":
			queueKey = queue.QueueNames.DeadLetter
		default:
			queueKey = queue.QueueNames.Normal
		}

		jobs, err := config.QueueManager.ListJobs(c.Request.Context(), queueKey, offset, offset+limit-1)
		if err != nil {
			log.Printf("Erro ao listar jobs: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "list_error",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"jobs":    jobs,
			"queue":   queueName,
			"limit":   limit,
			"offset":  offset,
		})
	}
}

// Funções auxiliares
func isValidVideoFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".mp4", ".avi", ".mov", ".mkv", ".webm", ".flv"}

	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	return false
}

func generateUploadID() string {
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		return fmt.Sprintf("upload_%d", time.Now().UnixNano())
	}
	return fmt.Sprintf("upload_%s", hex.EncodeToString(bytes))
}

// WorkerStatsHandler retorna estatísticas dos workers
func WorkerStatsHandler(workerPool *queue.WorkerPool) gin.HandlerFunc {
	return func(c *gin.Context) {
		if workerPool == nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"success": false,
				"error":   "worker_pool_unavailable",
				"message": "Worker pool não está disponível",
			})
			return
		}

		poolStats := workerPool.GetStats()
		workerStats := workerPool.GetWorkerStats()

		c.JSON(http.StatusOK, gin.H{
			"success":      true,
			"pool_stats":   poolStats,
			"worker_stats": workerStats,
		})
	}
}

// CleanupHandler força a limpeza das filas
func CleanupHandler(config *QueueConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		cleanupType := c.Query("type")

		switch cleanupType {
		case "expired":
			if err := config.QueueManager.CleanupExpiredJobs(c.Request.Context()); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"error":   "cleanup_expired_failed",
					"message": err.Error(),
				})
				return
			}
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "Limpeza de jobs expirados concluída",
			})

		case "orphaned":
			if err := config.QueueManager.CleanupOrphanedJobs(c.Request.Context()); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"error":   "cleanup_orphaned_failed",
					"message": err.Error(),
				})
				return
			}
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "Limpeza de jobs órfãos concluída",
			})

		case "all":
			// Executar ambas as limpezas
			if err := config.QueueManager.CleanupExpiredJobs(c.Request.Context()); err != nil {
				log.Printf("Erro na limpeza de jobs expirados: %v", err)
			}
			if err := config.QueueManager.CleanupOrphanedJobs(c.Request.Context()); err != nil {
				log.Printf("Erro na limpeza de jobs órfãos: %v", err)
			}
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "Limpeza completa concluída",
			})

		default:
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "invalid_cleanup_type",
				"message": "Tipo de limpeza deve ser: expired, orphaned ou all",
			})
		}
	}
}

// HealthCheckHandler verifica a saúde do sistema de filas
func HealthCheckHandler(config *QueueConfig, workerPool *queue.WorkerPool) gin.HandlerFunc {
	return func(c *gin.Context) {
		health := gin.H{
			"success":   true,
			"timestamp": time.Now().Unix(),
			"service":   "apexstream-queue",
		}

		// Verificar Redis
		if config.QueueManager != nil {
			// Tentar obter estatísticas para verificar conectividade
			if stats, err := config.QueueManager.GetQueueStats(c.Request.Context()); err != nil {
				health["redis"] = gin.H{
					"status": "unhealthy",
					"error":  err.Error(),
				}
				health["success"] = false
			} else {
				health["redis"] = gin.H{
					"status": "healthy",
					"stats":  stats,
				}
			}
		} else {
			health["redis"] = gin.H{
				"status": "unavailable",
			}
			health["success"] = false
		}

		// Verificar Worker Pool
		if workerPool != nil {
			poolStats := workerPool.GetStats()
			health["worker_pool"] = gin.H{
				"status":         "healthy",
				"total_workers":  poolStats.TotalWorkers,
				"active_workers": poolStats.ActiveWorkers,
				"uptime":         poolStats.Uptime.String(),
			}
		} else {
			health["worker_pool"] = gin.H{
				"status": "unavailable",
			}
			health["success"] = false
		}

		// Verificar diretórios
		health["directories"] = gin.H{
			"video_dir":     checkDirectory(config.VideoDir),
			"processed_dir": checkDirectory(config.ProcessedDir),
		}

		// Status HTTP baseado na saúde geral
		statusCode := http.StatusOK
		if !health["success"].(bool) {
			statusCode = http.StatusServiceUnavailable
		}

		c.JSON(statusCode, health)
	}
}

// PurgeQueueHandler limpa uma fila específica
func PurgeQueueHandler(config *QueueConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		queueName := c.Param("queue")
		if queueName == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "missing_queue_name",
				"message": "Nome da fila é obrigatório",
			})
			return
		}

		// Mapear nome da fila
		var queueKey string
		switch queueName {
		case "high":
			queueKey = queue.QueueNames.High
		case "normal":
			queueKey = queue.QueueNames.Normal
		case "low":
			queueKey = queue.QueueNames.Low
		case "completed":
			queueKey = queue.QueueNames.Completed
		case "failed":
			queueKey = queue.QueueNames.Failed
		case "dead_letter":
			queueKey = queue.QueueNames.DeadLetter
		default:
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "invalid_queue_name",
				"message": "Nome de fila inválido",
			})
			return
		}

		// Não permitir limpar fila de processamento
		if queueName == "processing" {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error":   "forbidden_operation",
				"message": "Não é possível limpar a fila de processamento",
			})
			return
		}

		// Limpar fila
		if err := config.QueueManager.PurgeQueue(c.Request.Context(), queueKey); err != nil {
			log.Printf("Erro ao limpar fila %s: %v", queueName, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "purge_error",
				"message": "Erro ao limpar fila",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": fmt.Sprintf("Fila %s limpa com sucesso", queueName),
		})
	}
}

// RetryJobHandler reprocessa um job falhado
func RetryJobHandler(config *QueueConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		jobID := c.Param("id")
		if jobID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "missing_job_id",
				"message": "ID do job é obrigatório",
			})
			return
		}

		// Buscar job
		job, err := config.QueueManager.GetJobStatus(c.Request.Context(), jobID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "job_not_found",
				"message": "Job não encontrado",
			})
			return
		}

		// Verificar se o job pode ser reprocessado
		if job.Status != queue.JobStatusFailed {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "invalid_job_status",
				"message": "Apenas jobs falhados podem ser reprocessados",
			})
			return
		}

		// Resetar job para reprocessamento
		job.Status = queue.JobStatusPending
		job.Error = ""
		job.Metadata.Attempts = 0
		job.StartedAt = nil
		job.CompletedAt = nil

		// Enfileirar novamente
		if err := config.QueueManager.Enqueue(c.Request.Context(), job); err != nil {
			log.Printf("Erro ao reprocessar job %s: %v", jobID, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "retry_error",
				"message": "Erro ao reprocessar job",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Job enfileirado para reprocessamento",
			"job_id":  jobID,
		})
	}
}

// checkDirectory verifica se um diretório existe e é acessível
func checkDirectory(path string) gin.H {
	if path == "" {
		return gin.H{
			"status": "not_configured",
		}
	}

	if _, err := os.Stat(path); os.IsNotExist(err) {
		return gin.H{
			"status": "missing",
			"path":   path,
		}
	} else if err != nil {
		return gin.H{
			"status": "error",
			"path":   path,
			"error":  err.Error(),
		}
	}

	return gin.H{
		"status": "healthy",
		"path":   path,
	}
}
