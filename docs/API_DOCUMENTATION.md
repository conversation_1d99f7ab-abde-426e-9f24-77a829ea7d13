# ApexStream API Documentation

## Visão Geral

O ApexStream é um sistema de processamento de vídeo que converte vídeos para formato HLS (HTTP Live Streaming) em múltiplas qualidades (480p, 720p, 1080p) de forma assíncrona, com progresso em tempo real via WebSocket.

## Base URL

```
http://localhost:8080/api/v1
```

## Autenticação

A API utiliza autenticação via API Key no header:

```
Authorization: Bearer YOUR_API_KEY
```

## Endpoints

### 1. Upload e Processamento de Vídeo

#### `POST /videos/upload`

Faz upload de um vídeo e inicia o processamento em múltiplas qualidades.

**Headers:**
```
Content-Type: multipart/form-data
Authorization: Bearer YOUR_API_KEY
```

**Body (form-data):**
- `video` (file): Arquivo de vídeo
- `quality_config` (string, opcional): JSON com configurações de qualidade

**Exemplo de quality_config:**
```json
{
  "process_in_parallel": true,
  "qualities": [
    {
      "name": "480p",
      "width": 854,
      "height": 480,
      "bitrate": 1000,
      "audio_rate": 128,
      "max_rate": 1200,
      "buf_size": 2000
    },
    {
      "name": "720p", 
      "width": 1280,
      "height": 720,
      "bitrate": 2500,
      "audio_rate": 128,
      "max_rate": 3000,
      "buf_size": 5000
    },
    {
      "name": "1080p",
      "width": 1920,
      "height": 1080,
      "bitrate": 5000,
      "audio_rate": 192,
      "max_rate": 6000,
      "buf_size": 10000
    }
  ]
}
```

**Resposta de Sucesso (202):**
```json
{
  "status": "success",
  "message": "Vídeo enviado para processamento",
  "job_id": "job_abc123def456",
  "websocket_url": "ws://localhost:8080/api/v1/ws/job_abc123def456"
}
```

### 2. Status do Job

#### `GET /jobs/{job_id}/status`

Obtém o status atual de um job de processamento.

**Headers:**
```
Authorization: Bearer YOUR_API_KEY
```

**Resposta de Sucesso (200):**
```json
{
  "status": "success",
  "data": {
    "id": "job_abc123def456",
    "status": "processing",
    "progress": 65.5,
    "current_quality": "720p",
    "quality_results": [
      {
        "quality": "480p",
        "success": true,
        "progress": 100,
        "status": "completed",
        "playlist_path": "/videos/processed/job_abc123def456/480p/playlist.m3u8",
        "segment_count": 45,
        "file_size": 15728640
      },
      {
        "quality": "720p",
        "success": false,
        "progress": 75,
        "status": "processing",
        "message": "Processando segmento 30/40"
      },
      {
        "quality": "1080p",
        "success": false,
        "progress": 0,
        "status": "pending",
        "message": "Aguardando processamento"
      }
    ],
    "master_playlist_path": "/videos/processed/job_abc123def456/master.m3u8",
    "duration": 180.5,
    "created_at": "2024-01-15T10:30:00Z",
    "started_at": "2024-01-15T10:30:05Z"
  }
}
```

### 3. WebSocket para Progresso em Tempo Real

#### `WS /ws/{job_id}`

Conecta ao WebSocket para receber atualizações de progresso em tempo real.

**URL:**
```
ws://localhost:8080/api/v1/ws/job_abc123def456?user_id=optional_user_id
```

**Mensagens Recebidas:**

**Conexão estabelecida:**
```json
{
  "type": "connected",
  "job_id": "job_abc123def456",
  "timestamp": "2024-01-15T10:30:00Z",
  "message": "Conectado ao sistema de progresso"
}
```

**Progresso de processamento:**
```json
{
  "type": "progress",
  "job_id": "job_abc123def456",
  "progress": 45.5,
  "status": "processing",
  "message": "Processando múltiplas qualidades",
  "current_quality": "720p",
  "quality_progress": [
    {
      "quality": "480p",
      "progress": 100,
      "status": "completed",
      "message": "Processamento concluído",
      "started_at": "2024-01-15T10:30:05Z",
      "completed_at": "2024-01-15T10:32:15Z"
    },
    {
      "quality": "720p",
      "progress": 65,
      "status": "processing",
      "message": "Processando segmento 26/40",
      "started_at": "2024-01-15T10:30:05Z"
    },
    {
      "quality": "1080p",
      "progress": 0,
      "status": "pending",
      "message": "Aguardando processamento"
    }
  ],
  "timestamp": "2024-01-15T10:32:30Z"
}
```

**Job concluído:**
```json
{
  "type": "progress",
  "job_id": "job_abc123def456",
  "progress": 100,
  "status": "completed",
  "message": "Processamento concluído com sucesso",
  "data": {
    "duration": 180.5,
    "total_segments": 135,
    "master_playlist_path": "/videos/processed/job_abc123def456/master.m3u8",
    "qualities_processed": ["480p", "720p", "1080p"]
  },
  "timestamp": "2024-01-15T10:35:00Z"
}
```

### 4. Download de Arquivos Processados

#### `GET /videos/{job_id}/master.m3u8`

Baixa a playlist master HLS.

#### `GET /videos/{job_id}/{quality}/playlist.m3u8`

Baixa a playlist de uma qualidade específica.

#### `GET /videos/{job_id}/{quality}/segment_{number}.ts`

Baixa um segmento específico de uma qualidade.

## Estados do Job

- `pending`: Job na fila aguardando processamento
- `processing`: Vídeo sendo processado
- `uploading`: Arquivos sendo enviados para storage (se habilitado)
- `completed`: Processamento concluído com sucesso
- `failed`: Processamento falhou
- `retrying`: Tentando reprocessar após falha

## Estados das Qualidades

- `pending`: Qualidade aguardando processamento
- `processing`: Qualidade sendo processada
- `completed`: Qualidade processada com sucesso
- `failed`: Falha no processamento da qualidade

## Códigos de Erro

- `400`: Requisição inválida
- `401`: Não autorizado (API key inválida)
- `404`: Recurso não encontrado
- `413`: Arquivo muito grande
- `422`: Dados inválidos
- `500`: Erro interno do servidor

## Exemplo de Uso Completo

```javascript
// 1. Upload do vídeo
const formData = new FormData();
formData.append('video', videoFile);
formData.append('quality_config', JSON.stringify({
  process_in_parallel: true,
  qualities: [
    { name: "480p", width: 854, height: 480, bitrate: 1000, audio_rate: 128, max_rate: 1200, buf_size: 2000 },
    { name: "720p", width: 1280, height: 720, bitrate: 2500, audio_rate: 128, max_rate: 3000, buf_size: 5000 },
    { name: "1080p", width: 1920, height: 1080, bitrate: 5000, audio_rate: 192, max_rate: 6000, buf_size: 10000 }
  ]
}));

const response = await fetch('/api/v1/videos/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: formData
});

const result = await response.json();
const jobId = result.job_id;

// 2. Conectar ao WebSocket para progresso
const ws = new WebSocket(`ws://localhost:8080/api/v1/ws/${jobId}`);

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('Progresso:', data);
  
  if (data.type === 'progress') {
    updateProgressUI(data.progress, data.quality_progress);
  }
};

// 3. Quando concluído, usar a playlist master
// http://localhost:8080/api/v1/videos/{jobId}/master.m3u8
```

## Limites e Considerações

- Tamanho máximo de arquivo: 100MB (configurável)
- Formatos suportados: MP4, AVI, MOV, MKV
- Processamento paralelo: Até 3 qualidades simultâneas
- Timeout de job: 5 minutos (configurável)
- Retenção de arquivos: 7 dias (configurável)
