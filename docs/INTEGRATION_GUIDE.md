# Guia de Integração - ApexStream

## Visão Geral

Este guia mostra como integrar o ApexStream em sua aplicação para processamento de vídeos em múltiplas qualidades com progresso em tempo real.

## Configuração Inicial

### 1. Variáveis de Ambiente

```bash
# Servidor
PORT=8080
ENVIRONMENT=production

# Processamento
VIDEO_DIR=./videos
MAX_FILE_SIZE=*********  # 100MB
ENABLE_FFMPEG=true

# Filas (Redis)
ENABLE_QUEUE=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
WORKER_POOL_SIZE=4

# Autenticação
ENABLE_AUTH=true
API_KEYS=key1,key2,key3

# Storage (opcional)
ENABLE_STORAGE=true
STORAGE_TYPE=r2  # ou local
R2_ENDPOINT=https://your-account.r2.cloudflarestorage.com
R2_ACCESS_KEY=your-access-key
R2_SECRET_KEY=your-secret-key
R2_BUCKET_NAME=your-bucket
R2_PUBLIC_URL=https://your-domain.com
```

### 2. Inicialização do Servidor

```bash
# Instalar dependências
go mod tidy

# Executar servidor
go run cmd/server/main.go
```

## Integração Frontend

### HTML + JavaScript

```html
<!DOCTYPE html>
<html>
<head>
    <title>ApexStream Upload</title>
    <style>
        .progress-container { margin: 20px 0; }
        .progress-bar { 
            width: 100%; 
            height: 20px; 
            background: #f0f0f0; 
            border-radius: 10px; 
            overflow: hidden; 
        }
        .progress-fill { 
            height: 100%; 
            background: #4CAF50; 
            transition: width 0.3s; 
        }
        .quality-progress { 
            margin: 10px 0; 
            padding: 10px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .status-completed { background-color: #d4edda; }
        .status-processing { background-color: #fff3cd; }
        .status-pending { background-color: #f8f9fa; }
        .status-failed { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h1>Upload de Vídeo - ApexStream</h1>
    
    <form id="uploadForm">
        <input type="file" id="videoFile" accept="video/*" required>
        <button type="submit">Upload e Processar</button>
    </form>

    <div id="progressContainer" style="display: none;">
        <h3>Progresso Geral</h3>
        <div class="progress-container">
            <div class="progress-bar">
                <div id="overallProgress" class="progress-fill" style="width: 0%"></div>
            </div>
            <span id="overallPercentage">0%</span>
        </div>

        <h3>Progresso por Qualidade</h3>
        <div id="qualityProgress"></div>

        <div id="status"></div>
    </div>

    <div id="result" style="display: none;">
        <h3>Vídeo Processado</h3>
        <video id="videoPlayer" controls width="800">
            Seu navegador não suporta o elemento de vídeo.
        </video>
        <p>Playlist Master: <a id="masterPlaylist" target="_blank">Baixar</a></p>
    </div>

    <script>
        const API_KEY = 'your-api-key-here';
        const BASE_URL = 'http://localhost:8080/api/v1';

        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('videoFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Selecione um arquivo de vídeo');
                return;
            }

            await uploadVideo(file);
        });

        async function uploadVideo(file) {
            const formData = new FormData();
            formData.append('video', file);
            
            // Configuração de qualidades personalizada
            const qualityConfig = {
                process_in_parallel: true,
                qualities: [
                    {
                        name: "480p",
                        width: 854,
                        height: 480,
                        bitrate: 1000,
                        audio_rate: 128,
                        max_rate: 1200,
                        buf_size: 2000
                    },
                    {
                        name: "720p",
                        width: 1280,
                        height: 720,
                        bitrate: 2500,
                        audio_rate: 128,
                        max_rate: 3000,
                        buf_size: 5000
                    },
                    {
                        name: "1080p",
                        width: 1920,
                        height: 1080,
                        bitrate: 5000,
                        audio_rate: 192,
                        max_rate: 6000,
                        buf_size: 10000
                    }
                ]
            };
            
            formData.append('quality_config', JSON.stringify(qualityConfig));

            try {
                const response = await fetch(`${BASE_URL}/videos/upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: formData
                });

                const result = await response.json();
                
                if (result.status === 'success') {
                    document.getElementById('progressContainer').style.display = 'block';
                    connectWebSocket(result.job_id);
                } else {
                    alert('Erro no upload: ' + result.message);
                }
            } catch (error) {
                alert('Erro na requisição: ' + error.message);
            }
        }

        function connectWebSocket(jobId) {
            const ws = new WebSocket(`ws://localhost:8080/api/v1/ws/${jobId}`);
            
            ws.onopen = () => {
                console.log('WebSocket conectado');
                updateStatus('Conectado ao sistema de progresso');
            };

            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                console.log('Mensagem recebida:', data);
                
                if (data.type === 'progress') {
                    updateProgress(data);
                } else if (data.type === 'connected') {
                    updateStatus(data.message);
                }
            };

            ws.onclose = () => {
                console.log('WebSocket desconectado');
            };

            ws.onerror = (error) => {
                console.error('Erro no WebSocket:', error);
                updateStatus('Erro na conexão WebSocket');
            };
        }

        function updateProgress(data) {
            // Atualizar progresso geral
            const overallProgress = document.getElementById('overallProgress');
            const overallPercentage = document.getElementById('overallPercentage');
            
            overallProgress.style.width = `${data.progress}%`;
            overallPercentage.textContent = `${data.progress.toFixed(1)}%`;

            // Atualizar progresso das qualidades
            if (data.quality_progress) {
                updateQualityProgress(data.quality_progress);
            }

            // Atualizar status
            updateStatus(data.message);

            // Se concluído, mostrar resultado
            if (data.status === 'completed' && data.progress >= 100) {
                showResult(data);
            }
        }

        function updateQualityProgress(qualityProgress) {
            const container = document.getElementById('qualityProgress');
            container.innerHTML = '';

            qualityProgress.forEach(quality => {
                const qualityDiv = document.createElement('div');
                qualityDiv.className = `quality-progress status-${quality.status}`;
                
                qualityDiv.innerHTML = `
                    <h4>${quality.quality}</h4>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${quality.progress}%"></div>
                        </div>
                        <span>${quality.progress.toFixed(1)}%</span>
                    </div>
                    <p><strong>Status:</strong> ${quality.status}</p>
                    <p><strong>Mensagem:</strong> ${quality.message}</p>
                    ${quality.started_at ? `<p><strong>Iniciado:</strong> ${new Date(quality.started_at).toLocaleString()}</p>` : ''}
                    ${quality.completed_at ? `<p><strong>Concluído:</strong> ${new Date(quality.completed_at).toLocaleString()}</p>` : ''}
                `;
                
                container.appendChild(qualityDiv);
            });
        }

        function updateStatus(message) {
            document.getElementById('status').innerHTML = `<p><strong>Status:</strong> ${message}</p>`;
        }

        function showResult(data) {
            const resultDiv = document.getElementById('result');
            const videoPlayer = document.getElementById('videoPlayer');
            const masterPlaylist = document.getElementById('masterPlaylist');
            
            if (data.data && data.data.master_playlist_path) {
                const masterUrl = `${BASE_URL}/videos/${data.job_id}/master.m3u8`;
                videoPlayer.src = masterUrl;
                masterPlaylist.href = masterUrl;
                resultDiv.style.display = 'block';
            }
        }
    </script>
</body>
</html>
```

## Integração Backend

### Node.js/Express

```javascript
const express = require('express');
const multer = require('multer');
const FormData = require('form-data');
const fetch = require('node-fetch');
const WebSocket = require('ws');

const app = express();
const upload = multer({ dest: 'uploads/' });

const APEXSTREAM_API_KEY = 'your-api-key';
const APEXSTREAM_BASE_URL = 'http://localhost:8080/api/v1';

// Upload de vídeo
app.post('/upload-video', upload.single('video'), async (req, res) => {
    try {
        const formData = new FormData();
        formData.append('video', fs.createReadStream(req.file.path));
        
        // Configuração personalizada de qualidades
        const qualityConfig = {
            process_in_parallel: true,
            qualities: [
                { name: "480p", width: 854, height: 480, bitrate: 1000, audio_rate: 128, max_rate: 1200, buf_size: 2000 },
                { name: "720p", width: 1280, height: 720, bitrate: 2500, audio_rate: 128, max_rate: 3000, buf_size: 5000 },
                { name: "1080p", width: 1920, height: 1080, bitrate: 5000, audio_rate: 192, max_rate: 6000, buf_size: 10000 }
            ]
        };
        
        formData.append('quality_config', JSON.stringify(qualityConfig));

        const response = await fetch(`${APEXSTREAM_BASE_URL}/videos/upload`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${APEXSTREAM_API_KEY}`
            },
            body: formData
        });

        const result = await response.json();
        
        if (result.status === 'success') {
            // Monitorar progresso via WebSocket
            monitorProgress(result.job_id);
            res.json({ success: true, job_id: result.job_id });
        } else {
            res.status(400).json({ success: false, error: result.message });
        }
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

function monitorProgress(jobId) {
    const ws = new WebSocket(`ws://localhost:8080/api/v1/ws/${jobId}`);
    
    ws.on('open', () => {
        console.log(`Monitorando progresso do job ${jobId}`);
    });
    
    ws.on('message', (data) => {
        const message = JSON.parse(data);
        console.log(`Progresso ${jobId}:`, message);
        
        if (message.type === 'progress') {
            // Notificar cliente via Socket.IO, webhook, etc.
            notifyClient(jobId, message);
            
            if (message.status === 'completed') {
                console.log(`Job ${jobId} concluído!`);
                ws.close();
            }
        }
    });
    
    ws.on('error', (error) => {
        console.error(`Erro no WebSocket para job ${jobId}:`, error);
    });
}

function notifyClient(jobId, progressData) {
    // Implementar notificação para o cliente
    // Exemplo: Socket.IO, webhook, database update, etc.
}

app.listen(3000, () => {
    console.log('Servidor rodando na porta 3000');
});
```

### Python/FastAPI

```python
from fastapi import FastAPI, UploadFile, File
import httpx
import asyncio
import websockets
import json

app = FastAPI()

APEXSTREAM_API_KEY = "your-api-key"
APEXSTREAM_BASE_URL = "http://localhost:8080/api/v1"

@app.post("/upload-video")
async def upload_video(video: UploadFile = File(...)):
    try:
        # Configuração de qualidades
        quality_config = {
            "process_in_parallel": True,
            "qualities": [
                {"name": "480p", "width": 854, "height": 480, "bitrate": 1000, "audio_rate": 128, "max_rate": 1200, "buf_size": 2000},
                {"name": "720p", "width": 1280, "height": 720, "bitrate": 2500, "audio_rate": 128, "max_rate": 3000, "buf_size": 5000},
                {"name": "1080p", "width": 1920, "height": 1080, "bitrate": 5000, "audio_rate": 192, "max_rate": 6000, "buf_size": 10000}
            ]
        }
        
        files = {"video": (video.filename, video.file, video.content_type)}
        data = {"quality_config": json.dumps(quality_config)}
        headers = {"Authorization": f"Bearer {APEXSTREAM_API_KEY}"}
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{APEXSTREAM_BASE_URL}/videos/upload",
                files=files,
                data=data,
                headers=headers
            )
            
        result = response.json()
        
        if result["status"] == "success":
            # Iniciar monitoramento em background
            asyncio.create_task(monitor_progress(result["job_id"]))
            return {"success": True, "job_id": result["job_id"]}
        else:
            return {"success": False, "error": result["message"]}
            
    except Exception as e:
        return {"success": False, "error": str(e)}

async def monitor_progress(job_id: str):
    uri = f"ws://localhost:8080/api/v1/ws/{job_id}"
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"Monitorando progresso do job {job_id}")
            
            async for message in websocket:
                data = json.loads(message)
                print(f"Progresso {job_id}: {data}")
                
                if data["type"] == "progress":
                    # Notificar cliente
                    await notify_client(job_id, data)
                    
                    if data["status"] == "completed":
                        print(f"Job {job_id} concluído!")
                        break
                        
    except Exception as e:
        print(f"Erro no WebSocket para job {job_id}: {e}")

async def notify_client(job_id: str, progress_data: dict):
    # Implementar notificação para o cliente
    # Exemplo: WebSocket para frontend, webhook, database update, etc.
    pass
```

## Considerações de Produção

### 1. Monitoramento
- Implementar logs estruturados
- Monitorar uso de CPU/memória durante processamento
- Alertas para jobs que falham frequentemente

### 2. Escalabilidade
- Usar Redis Cluster para alta disponibilidade
- Implementar load balancer para múltiplas instâncias
- Considerar processamento distribuído

### 3. Segurança
- Validar tipos de arquivo rigorosamente
- Implementar rate limiting
- Usar HTTPS em produção
- Rotacionar API keys regularmente

### 4. Performance
- Otimizar configurações do FFmpeg
- Usar SSD para armazenamento temporário
- Implementar cache para arquivos processados
- Considerar CDN para distribuição
